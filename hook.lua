-- local ReplicatedStorage = game:GetService("ReplicatedStorage")
local Players = game:GetService("Players")

-- Global variables
local Shop = nil
local originalFunctions = {}
local isHooked = false

-- Functions to exclude from hooking
local excludedFunctions = {
    ["GetShopDataFolder"] = true,
    ["GetLocalShopFolder"] = true
}

-- Helper function to add functions to exclusion list
local function addExcludedFunction(functionName)
    excludedFunctions[functionName] = true
    print("[SHOP HOOK] Added '" .. functionName .. "' to exclusion list")
end

-- Helper function to remove functions from exclusion list
local function removeExcludedFunction(functionName)
    excludedFunctions[functionName] = nil
    print("[SHOP HOOK] Removed '" .. functionName .. "' from exclusion list")
end

-- Function to safely load the Shop module with error checking
local function loadShopModule()
    print("[SHOP HOOK] Attempting to load Shop module...")

    -- Check if ReplicatedStorage exists
    local success, replicatedStorage = pcall(function()
        return game:GetService("ReplicatedStorage")
    end)

    if not success then
        print("[SHOP HOOK] ERROR: Failed to get ReplicatedStorage service: " .. tostring(replicatedStorage))
        return false
    end

    if not replicatedStorage then
        print("[SHOP HOOK] ERROR: ReplicatedStorage service is nil")
        return false
    end

    print("[SHOP HOOK] ReplicatedStorage found: " .. tostring(replicatedStorage))

    -- Check if Shared folder exists
    local shared = replicatedStorage:FindFirstChild("Shared")
    if not shared then
        print("[SHOP HOOK] ERROR: Shared folder not found in ReplicatedStorage")
        print("[SHOP HOOK] Available children in ReplicatedStorage:")
        for _, child in pairs(replicatedStorage:GetChildren()) do
            print("  - " .. child.Name .. " (" .. child.ClassName .. ")")
        end
        return false
    end

    print("[SHOP HOOK] Shared folder found: " .. tostring(shared))

    -- Check if Shop module exists
    local shopModule = shared:FindFirstChild("Shop")
    if not shopModule then
        print("[SHOP HOOK] ERROR: Shop module not found in Shared folder")
        print("[SHOP HOOK] Available children in Shared:")
        for _, child in pairs(shared:GetChildren()) do
            print("  - " .. child.Name .. " (" .. child.ClassName .. ")")
        end
        return false
    end

    print("[SHOP HOOK] Shop module found: " .. tostring(shopModule) .. " (" .. shopModule.ClassName .. ")")

    -- Try to require the Shop module
    local requireSuccess, shopResult = pcall(function()
        return require(shopModule)
    end)

    if not requireSuccess then
        print("[SHOP HOOK] ERROR: Failed to require Shop module: " .. tostring(shopResult))
        return false
    end

    if not shopResult then
        print("[SHOP HOOK] ERROR: Shop module require returned nil")
        return false
    end

    if type(shopResult) ~= "table" then
        print("[SHOP HOOK] ERROR: Shop module is not a table, got: " .. type(shopResult))
        return false
    end

    print("[SHOP HOOK] Shop module loaded successfully!")
    print("[SHOP HOOK] Shop module type: " .. type(shopResult))

    -- Count and list available functions
    local functionCount = 0
    print("[SHOP HOOK] Available functions in Shop module:")
    for key, value in pairs(shopResult) do
        print("  - " .. key .. " (" .. type(value) .. ")")
        if type(value) == "function" then
            functionCount = functionCount + 1
        end
    end
    print("[SHOP HOOK] Found " .. functionCount .. " functions to hook")

    Shop = shopResult
    return true
end

-- Function to recursively convert a table to a readable string
local function tableToString(tbl, depth, maxDepth, visited)
    depth = depth or 0
    maxDepth = maxDepth or 4
    visited = visited or {}

    -- Prevent infinite recursion
    if visited[tbl] then
        return "{...circular reference...}"
    end

    -- Limit depth to prevent overly verbose output
    if depth >= maxDepth then
        return "{...max depth reached...}"
    end

    visited[tbl] = true

    local parts = {}
    local indent = string.rep("  ", depth)
    local nextIndent = string.rep("  ", depth + 1)

    -- Handle empty tables
    local isEmpty = true
    for _ in pairs(tbl) do
        isEmpty = false
        break
    end

    if isEmpty then
        visited[tbl] = nil
        return "{}"
    end

    table.insert(parts, "{")

    -- Sort keys for consistent output
    local keys = {}
    for k in pairs(tbl) do
        table.insert(keys, k)
    end

    -- Sort keys, putting numbers first, then strings
    table.sort(keys, function(a, b)
        local aType, bType = type(a), type(b)
        if aType == bType then
            return tostring(a) < tostring(b)
        else
            return aType == "number"
        end
    end)

    for i, key in ipairs(keys) do
        local value = tbl[key]
        local keyStr, valueStr

        -- Format the key
        if type(key) == "string" then
            keyStr = '"' .. key .. '"'
        else
            keyStr = tostring(key)
        end

        -- Format the value
        if type(value) == "table" then
            valueStr = tableToString(value, depth + 1, maxDepth, visited)
        elseif type(value) == "string" then
            valueStr = '"' .. tostring(value) .. '"'
        elseif type(value) == "function" then
            valueStr = "function"
        elseif type(value) == "userdata" then
            valueStr = "userdata"
        else
            valueStr = tostring(value)
        end

        -- Add comma for all but the last item
        local comma = (i < #keys) and "," or ""
        table.insert(parts, nextIndent .. keyStr .. " = " .. valueStr .. comma)
    end

    table.insert(parts, indent .. "}")

    visited[tbl] = nil
    return table.concat(parts, "\n")
end

-- Function to convert arguments to string for logging
local function argsToString(...)
    local args = {...}
    local argStrings = {}

    for i, arg in ipairs(args) do
        local argStr
        if type(arg) == "string" then
            argStr = '"' .. tostring(arg) .. '"'
        elseif type(arg) == "table" then
            argStr = tableToString(arg)
        elseif type(arg) == "function" then
            argStr = "function"
        elseif type(arg) == "userdata" then
            argStr = "userdata"
        else
            argStr = tostring(arg)
        end

        -- For tables, add a newline and indentation for better readability
        if type(arg) == "table" and string.find(argStr, "\n") then
            argStr = "\n  " .. string.gsub(argStr, "\n", "\n  ")
        end

        table.insert(argStrings, "arg" .. i .. ": " .. argStr)
    end

    return table.concat(argStrings, ", ")
end

-- Function to create a hook for a given function
local function createHook(functionName, originalFunction)
    return function(...)
        -- Log the function call with all arguments
        local argsStr = argsToString(...)

        -- Check if we have complex arguments (tables) that need multi-line formatting
        if string.find(argsStr, "\n") then
            print("[SHOP HOOK] " .. functionName .. " called with arguments:")
            print(argsStr)
        else
            print("[SHOP HOOK] " .. functionName .. "(" .. argsStr .. ")")
        end

        -- Call the original function and return its result(s)
        return originalFunction(...)
    end
end

-- Function to hook all functions in the Shop module
local function hookAllShopFunctions()
    if not Shop then
        print("[SHOP HOOK] ERROR: Shop module is nil, cannot hook functions")
        return false
    end

    if type(Shop) ~= "table" then
        print("[SHOP HOOK] ERROR: Shop is not a table, got: " .. type(Shop))
        return false
    end

    print("[SHOP HOOK] Starting to hook all Shop module functions...")

    local hookedCount = 0
    local skippedCount = 0

    for key, value in pairs(Shop) do
        if type(value) == "function" then
            -- Check if this function should be excluded from hooking
            if excludedFunctions[key] then
                print("[SHOP HOOK] Skipping function (excluded): " .. key)
                skippedCount = skippedCount + 1
            else
                print("[SHOP HOOK] Hooking function: " .. key)

                -- Store the original function
                originalFunctions[key] = value

                -- Replace with our hooked version
                Shop[key] = createHook(key, value)
                hookedCount = hookedCount + 1
            end
        end
    end

    print("[SHOP HOOK] Finished hooking " .. hookedCount .. " functions (skipped " .. skippedCount .. " excluded functions)")
    isHooked = true
    return true
end

-- Function to attempt loading and hooking with retry mechanism
local function initializeHooks()
    print("[SHOP HOOK] Initializing hooks...")

    -- Try to load the Shop module
    if not loadShopModule() then
        print("[SHOP HOOK] Failed to load Shop module on first attempt")
        return false
    end

    -- Try to hook all functions
    if not hookAllShopFunctions() then
        print("[SHOP HOOK] Failed to hook Shop functions")
        return false
    end

    return true
end

-- Function to retry initialization with delays
local function retryInitialization()
    local maxRetries = 10
    local retryDelay = 1 -- seconds

    for attempt = 1, maxRetries do
        print("[SHOP HOOK] Initialization attempt " .. attempt .. "/" .. maxRetries)

        if initializeHooks() then
            print("[SHOP HOOK] Successfully initialized on attempt " .. attempt)
            return true
        end

        if attempt < maxRetries then
            print("[SHOP HOOK] Retrying in " .. retryDelay .. " seconds...")
            -- Simple delay mechanism for Roblox
            local delaySuccess, delayResult = pcall(function()
                if task and task.wait then
                    task.wait(retryDelay)
                elseif wait then
                    wait(retryDelay)
                else
                    -- Fallback busy wait
                    local startTime = os.clock()
                    while os.clock() - startTime < retryDelay do
                        -- Simple delay loop
                    end
                end
            end)

            if not delaySuccess then
                print("[SHOP HOOK] Warning: Delay function failed: " .. tostring(delayResult))
            end

            retryDelay = retryDelay * 1.5 -- Exponential backoff
        end
    end

    print("[SHOP HOOK] ERROR: Failed to initialize after " .. maxRetries .. " attempts")
    return false
end

-- Function to set up metatable for automatic hooking of new functions
local function setupAutoHooking()
    if not Shop or type(Shop) ~= "table" then
        print("[SHOP HOOK] Cannot setup auto-hooking: Shop is not a valid table")
        return false
    end

    print("[SHOP HOOK] Setting up automatic hooking for new functions...")

    local shopMetatable = {
        __newindex = function(table, key, value)
            if type(value) == "function" and not originalFunctions[key] then
                -- Check if this function should be excluded from auto-hooking
                if excludedFunctions[key] then
                    print("[SHOP HOOK] New function detected: " .. key .. " - Skipping (excluded from hooking)")
                    rawset(table, key, value)
                else
                    print("[SHOP HOOK] New function detected: " .. key .. " - Auto-hooking...")
                    originalFunctions[key] = value
                    rawset(table, key, createHook(key, value))
                end
            else
                rawset(table, key, value)
            end
        end
    }

    setmetatable(Shop, shopMetatable)
    print("[SHOP HOOK] Auto-hooking metatable installed successfully")
    return true
end

-- Start the initialization process
print("[SHOP HOOK] Starting Shop module hook initialization...")
if retryInitialization() then
    -- Try to set up auto-hooking after successful initialization
    setupAutoHooking()
    print("[SHOP HOOK] All initialization completed successfully!")
else
    print("[SHOP HOOK] Initialization failed - hooks are not active")
end
