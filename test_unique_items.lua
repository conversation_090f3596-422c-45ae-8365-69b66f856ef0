-- Test script to verify unique item identification works correctly
-- This script simulates the key parts of the auto_buy.lua logic

-- Helper function to create unique item identifier
local function getItemId(item)
    return item.name .. "_" .. item.dye
end

-- Sample items with duplicate names but different dyes
local testItems = {
    { name = "DarkWings", price = 4000000, dye = "FFADED" },
    { name = "DarkWings", price = 4000000, dye = "E6E6E6" },
    { name = "DarkWings", price = 4000000, dye = "1E1E1E" },
    { name = "DarkWings", price = 4000000, dye = "FFFFFF" },
    { name = "BodyAura", price = 3000000, dye = "FFADED" },
    { name = "BodyAura", price = 3000000, dye = "E6E6E6" },
    { name = "BodyAura", price = 3000000, dye = "1E1E1E" },
}

-- Test the unique ID generation
print("=== Testing Unique Item ID Generation ===")
for _, item in ipairs(testItems) do
    local itemId = getItemId(item)
    print("Item: " .. item.name .. " | Dye: " .. item.dye .. " | Unique ID: " .. itemId)
end

-- Test toggle states with unique IDs
print("\n=== Testing Toggle States ===")
local toggleStates = {}

-- Simulate toggling different items
toggleStates[getItemId(testItems[1])] = true  -- DarkWings_FFADED
toggleStates[getItemId(testItems[3])] = true  -- DarkWings_1E1E1E
toggleStates[getItemId(testItems[5])] = true  -- BodyAura_FFADED

print("Toggle States:")
for itemId, state in pairs(toggleStates) do
    print("  " .. itemId .. " = " .. tostring(state))
end

-- Test that we can distinguish between items with same name
print("\n=== Testing Item Selection ===")
local selectedItems = {}

for _, item in ipairs(testItems) do
    local itemId = getItemId(item)
    if toggleStates[itemId] then
        table.insert(selectedItems, item)
        print("Selected: " .. item.name .. " (Dye: " .. item.dye .. ")")
    end
end

print("\nTotal selected items: " .. #selectedItems)

-- Verify each selected item is unique
print("\n=== Verifying Uniqueness ===")
for i, item in ipairs(selectedItems) do
    print(i .. ". " .. item.name .. " with dye " .. item.dye .. " (Price: " .. item.price .. ")")
end

print("\n✅ Test completed! Each item with the same name but different dye is now uniquely identifiable.")
